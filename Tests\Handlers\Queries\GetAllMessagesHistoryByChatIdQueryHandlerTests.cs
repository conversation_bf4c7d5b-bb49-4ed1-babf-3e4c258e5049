using HcAgents.Domain.Abstractions;
using HcAgents.Domain.Entities;
using Moq;

namespace Tests.Handlers.Queries;

public class GetAllMessagesHistoryByChatIdQueryHandlerTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMessageRepository> _mockMessageRepository;
    private readonly GetAllMessagesHistoryByChatIdQueryHandler _handler;

    public GetAllMessagesHistoryByChatIdQueryHandlerTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMessageRepository = new Mock<IMessageRepository>();
        _mockUnitOfWork.Setup(x => x.MessageRepository).Returns(_mockMessageRepository.Object);
        _handler = new GetAllMessagesHistoryByChatIdQueryHandler(_mockUnitOfWork.Object);
    }

    [Fact]
    public async Task Handle_WithValidChatId_ShouldReturnMessagesForChat()
    {
        // Arrange
        var chatId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var createdAt = DateTime.Now;

        var expectedMessages = new List<Message>
        {
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Hello, how are you?",
                ChatId = chatId,
                IsUserMessage = true,
                CreatedAt = createdAt
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "I'm doing well, thank you! How can I help you today?",
                ChatId = chatId,
                IsUserMessage = false,
                CreatedAt = createdAt.AddMinutes(1)
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Can you help me with a coding problem?",
                ChatId = chatId,
                IsUserMessage = true,
                CreatedAt = createdAt.AddMinutes(2)
            }
        };

        var query = new GetAllMessagesHistoryByChatIdQuery
        {
            ChatId = chatId,
            ItensPerPage = 25,
            Page = 1
        };

        _mockMessageRepository
            .Setup(x => x.GetMessagesByChatId(chatId))
            .ReturnsAsync(expectedMessages);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());
        Assert.All(result, message => Assert.Equal(chatId, message.ChatId));
        
        // Verify the sequence of messages (user, bot, user)
        var messagesList = result.ToList();
        Assert.True(messagesList[0].IsUserMessage);
        Assert.False(messagesList[1].IsUserMessage);
        Assert.True(messagesList[2].IsUserMessage);

        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(chatId), Times.Once);
    }

    [Fact]
    public async Task Handle_WithChatIdThatHasNoMessages_ShouldReturnEmptyList()
    {
        // Arrange
        var chatId = Guid.NewGuid();
        var emptyMessages = new List<Message>();

        var query = new GetAllMessagesHistoryByChatIdQuery
        {
            ChatId = chatId,
            ItensPerPage = 25,
            Page = 1
        };

        _mockMessageRepository
            .Setup(x => x.GetMessagesByChatId(chatId))
            .ReturnsAsync(emptyMessages);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
        
        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(chatId), Times.Once);
    }

    [Fact]
    public async Task Handle_WithDifferentChatIds_ShouldOnlyReturnMessagesForSpecificChat()
    {
        // Arrange
        var targetChatId = Guid.NewGuid();
        var otherChatId = Guid.NewGuid();
        var createdAt = DateTime.Now;

        var messagesForTargetChat = new List<Message>
        {
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Message for target chat",
                ChatId = targetChatId,
                IsUserMessage = true,
                CreatedAt = createdAt
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Bot response for target chat",
                ChatId = targetChatId,
                IsUserMessage = false,
                CreatedAt = createdAt.AddMinutes(1)
            }
        };

        var query = new GetAllMessagesHistoryByChatIdQuery
        {
            ChatId = targetChatId,
            ItensPerPage = 25,
            Page = 1
        };

        _mockMessageRepository
            .Setup(x => x.GetMessagesByChatId(targetChatId))
            .ReturnsAsync(messagesForTargetChat);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.All(result, message => Assert.Equal(targetChatId, message.ChatId));
        
        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(targetChatId), Times.Once);
        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(otherChatId), Times.Never);
    }

    [Fact]
    public async Task Handle_WithMixedMessageTypes_ShouldReturnBothUserAndBotMessages()
    {
        // Arrange
        var chatId = Guid.NewGuid();
        var createdAt = DateTime.Now;

        var mixedMessages = new List<Message>
        {
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "User message 1",
                ChatId = chatId,
                IsUserMessage = true,
                CreatedAt = createdAt
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Bot response 1",
                ChatId = chatId,
                IsUserMessage = false,
                CreatedAt = createdAt.AddMinutes(1)
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "User message 2",
                ChatId = chatId,
                IsUserMessage = true,
                CreatedAt = createdAt.AddMinutes(2)
            },
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Bot response 2",
                ChatId = chatId,
                IsUserMessage = false,
                CreatedAt = createdAt.AddMinutes(3)
            }
        };

        var query = new GetAllMessagesHistoryByChatIdQuery
        {
            ChatId = chatId,
            ItensPerPage = 25,
            Page = 1
        };

        _mockMessageRepository
            .Setup(x => x.GetMessagesByChatId(chatId))
            .ReturnsAsync(mixedMessages);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count());
        
        var messagesList = result.ToList();
        var userMessages = messagesList.Where(m => m.IsUserMessage).ToList();
        var botMessages = messagesList.Where(m => !m.IsUserMessage).ToList();
        
        Assert.Equal(2, userMessages.Count);
        Assert.Equal(2, botMessages.Count);
        
        Assert.All(userMessages, m => Assert.True(m.IsUserMessage));
        Assert.All(botMessages, m => Assert.False(m.IsUserMessage));
        
        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(chatId), Times.Once);
    }

    [Fact]
    public async Task Handle_WithPaginationParameters_ShouldStillCallRepositoryCorrectly()
    {
        // Arrange
        var chatId = Guid.NewGuid();
        var messages = new List<Message>
        {
            new Message
            {
                Id = Guid.NewGuid(),
                Content = "Test message",
                ChatId = chatId,
                IsUserMessage = true,
                CreatedAt = DateTime.Now
            }
        };

        var query = new GetAllMessagesHistoryByChatIdQuery
        {
            ChatId = chatId,
            ItensPerPage = 10,
            Page = 2
        };

        _mockMessageRepository
            .Setup(x => x.GetMessagesByChatId(chatId))
            .ReturnsAsync(messages);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        
        // Note: The current implementation doesn't use pagination parameters in the repository call
        // This test verifies that the handler still works correctly regardless of pagination values
        _mockMessageRepository.Verify(x => x.GetMessagesByChatId(chatId), Times.Once);
    }
}

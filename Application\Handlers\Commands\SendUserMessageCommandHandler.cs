using HcAgents.Domain.Abstractions;
using HcAgents.Domain.Entities;
using MediatR;

public class SendUserMessageCommandHandler : IRequestHandler<SendUserMessageCommand, Message>
{
    private readonly IUnitOfWork _unitOfWork;

    public SendUserMessageCommandHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Message> Handle(
        SendUserMessageCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var message = await _unitOfWork.MessageRepository.AddMessage(
                new Message
                {
                    Content = request.Content,
                    ChatId = request.ChatId,
                    IsUserMessage = true,
                    CreatedAt = DateTime.Now,
                }
            );

            await _unitOfWork.CommitAsync();

            return message;
        }
        catch (Exception)
        {
            throw;
        }
    }
}
